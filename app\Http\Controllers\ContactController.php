<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactMail;

class ContactController extends Controller
{
    /**
     * Afficher le formulaire de contact
     */
    public function show()
    {
        return view('contact.form');
    }

    /**
     * Traiter l'envoi du formulaire de contact
     */
    public function send(Request $request)
    {
        // Validation des données
        $validatedData = $request->validate([
            'nom' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'sujet' => 'required|string|max:255',
            'message' => 'required|string|min:10',
        ]);

        try {
            // Envoyer l'email à l'administrateur
            Mail::to('<EMAIL>')->send(new ContactMail($validatedData));
            
            return redirect()->back()->with('success', 'Votre message a été envoyé avec succès !');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de l\'envoi du message. Veuillez réessayer.');
        }
    }
}
