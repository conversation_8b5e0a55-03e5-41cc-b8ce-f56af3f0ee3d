<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use Illuminate\Http\Request;

class EmployeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $employes = Employe::all();
        return view("employes.index", compact("employes"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view("employes.create");
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'prenom' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:employes,email',
            'poste' => 'required|string|max:255',
            'date_embauche' => 'required|date',
            'salaire' => 'required|numeric|min:0',
        ]);

        Employe::create($request->all());
        return redirect()->route("employes.index")->with('success', 'Employé créé avec succès');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $employe = Employe::findOrFail($id);
        return view('employes.show', compact('employe'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $employe = Employe::findOrFail($id);
        return view('employes.edit', compact('employe'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $employe = Employe::findOrFail($id);

        $request->validate([
            'prenom' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:employes,email,' . $id,
            'poste' => 'required|string|max:255',
            'date_embauche' => 'required|date',
            'salaire' => 'required|numeric|min:0',
        ]);

        $employe->update($request->all());
        return redirect()->route("employes.index")->with('success', 'Employé modifié avec succès');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $employe = Employe::findOrFail($id);
        $employe->delete();
        return redirect()->route('employes.index')->with('success', 'Employé supprimé avec succès');
    }

}
