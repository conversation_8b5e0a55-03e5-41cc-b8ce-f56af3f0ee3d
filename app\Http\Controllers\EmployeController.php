<?php

namespace App\Http\Controllers;

use App\Models\Employe;
use Illuminate\Http\Request;

class EmployeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $employer = Employe::all();
        return view("index", compact("employer"));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view("create");
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'prenom' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:employes,email',
            'poste' => 'required|string|max:255',
            'date_embauche' => 'required|date',
            'salaire' => 'required|numeric|min:0',
        ]);

        Employe::created($request->all());
        return redirect()->route("index");
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $employe = Employe::find($id);
        return view('employe.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $employe = Employe::find($id);
        return view('employe.edit', compact('employe'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'prenom' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:employes,email',
            'poste' => 'required|string|max:255',
            'date_embauche' => 'required|date',
            'salaire' => 'required|numeric|min:0',
        ]);
        Employe::find($id);
        Employe::update($request->all());
        return redirect()->route("index");
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $employee = Employe::findOrFail($id);
        $employee->delete();
        return redirect()->route('employees.index')->with('success', 'Employé supprimé avec succès');
    }

}
