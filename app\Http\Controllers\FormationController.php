<?php

namespace App\Http\Controllers;

use App\Models\Formation;
use App\Models\Employe;
use Illuminate\Http\Request;

class FormationController extends Controller
{
    /**
     * Afficher les formations d'un employé spécifique
     */
    public function showFormationsByEmploye($employe_id)
    {
        // Récupérer l'employé
        $employe = Employe::findOrFail($employe_id);
        
        // Récupérer ses formations
        $formations = $employe->formations;
        
        // Retourner la vue avec les formations
        return view('formations.by-employe', compact('employe', 'formations'));
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $formations = Formation::with('employe')->get();
        return view('formations.index', compact('formations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $employes = Employe::all();
        return view('formations.create', compact('employes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'employe_id' => 'required|exists:employes,id',
            'nom' => 'required|string|max:255',
            'description' => 'required|string',
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after:date_debut',
        ]);

        Formation::create($request->all());
        return redirect()->route('formations.index')->with('success', 'Formation créée avec succès');
    }

    /**
     * Display the specified resource.
     */
    public function show(Formation $formation)
    {
        return view('formations.show', compact('formation'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Formation $formation)
    {
        $employes = Employe::all();
        return view('formations.edit', compact('formation', 'employes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Formation $formation)
    {
        $request->validate([
            'employe_id' => 'required|exists:employes,id',
            'nom' => 'required|string|max:255',
            'description' => 'required|string',
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after:date_debut',
        ]);

        $formation->update($request->all());
        return redirect()->route('formations.index')->with('success', 'Formation modifiée avec succès');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Formation $formation)
    {
        $formation->delete();
        return redirect()->route('formations.index')->with('success', 'Formation supprimée avec succès');
    }
}
