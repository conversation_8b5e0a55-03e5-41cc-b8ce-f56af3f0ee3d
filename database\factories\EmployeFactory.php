<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Employe>
 */
class EmployeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $postes = [
            '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Junior', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Senior', 'Chef de Projet',
            '<PERSON><PERSON><PERSON>', 'Designer', '<PERSON>O<PERSON>', 'Testeur', 'Scrum Master'
        ];

        return [
            'prenom' => $this->faker->firstName,
            'nom' => $this->faker->lastName,
            'email' => $this->faker->unique()->safeEmail,
            'poste' => $this->faker->randomElement($postes),
            'date_embauche' => $this->faker->dateTimeBetween('-5 years', 'now')->format('Y-m-d'),
            'salaire' => $this->faker->randomFloat(2, 25000, 80000),
        ];
    }
}
