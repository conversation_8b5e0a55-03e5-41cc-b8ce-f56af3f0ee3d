<?php

namespace Database\Seeders;

use App\Models\Employe;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmployeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Employe::create([
            'prenom' => 'Ali',
            'nom' => 'Ben<PERSON>',
            'email' => '<EMAIL>',
            'poste' => 'Développeur',
            'date_embauche' => '2023-01-15',
            'salaire' => 35000.00
        ]);

        Employe::create([
            'prenom' => '<PERSON>',
            'nom' => '<PERSON>',
            'email' => '<EMAIL>',
            'poste' => 'Chef de Projet',
            'date_embauche' => '2022-03-10',
            'salaire' => 55000.00,
        ]);

        Employe::create([
            'prenom' => '<PERSON>',
            'nom' => 'Durand',
            'email' => '<EMAIL>',
            'poste' => 'Analyste',
            'date_embauche' => '2023-06-01',
            'salaire' => 38000.00,
        ]);
    }
}

