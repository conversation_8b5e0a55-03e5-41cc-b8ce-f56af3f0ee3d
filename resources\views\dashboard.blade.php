<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Statistiques -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="text-3xl font-bold text-blue-600">
                                {{ \App\Models\Employe::count() }}
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Employés</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="text-3xl font-bold text-green-600">
                                {{ \App\Models\Formation::count() }}
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total Formations</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="text-3xl font-bold text-purple-600">
                                {{ number_format(\App\Models\Employe::avg('salaire'), 0, ',', ' ') }} €
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Salaire Moyen</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Actions Rapides</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <a href="{{ route('employes.index') }}"
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded text-center">
                            📋 Voir tous les employés
                        </a>
                        <a href="{{ route('employes.create') }}"
                           class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded text-center">
                            ➕ Ajouter un employé
                        </a>
                        <a href="{{ route('formations.index') }}"
                           class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded text-center">
                            🎓 Voir les formations
                        </a>
                        <a href="{{ route('contact.form') }}"
                           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-4 rounded text-center">
                            📧 Contact
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
