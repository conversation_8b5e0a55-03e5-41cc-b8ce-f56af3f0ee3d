@extends('layouts.app')

@section('title', 'Liste des Employés')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Liste des Employés</h1>
    <a href="{{ route('employes.create') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Ajouter un employé
    </a>
</div>

@if($employes->count() > 0)
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>ID</th>
                    <th>Prénom</th>
                    <th>Nom</th>
                    <th>Email</th>
                    <th>Poste</th>
                    <th>Date d'embauche</th>
                    <th>Salaire</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($employes as $employe)
                <tr>
                    <td>{{ $employe->id }}</td>
                    <td>{{ $employe->prenom }}</td>
                    <td>{{ $employe->nom }}</td>
                    <td>{{ $employe->email }}</td>
                    <td>{{ $employe->poste }}</td>
                    <td>{{ \Carbon\Carbon::parse($employe->date_embauche)->format('d/m/Y') }}</td>
                    <td>{{ number_format($employe->salaire, 2, ',', ' ') }} €</td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{{ route('employes.show', $employe->id) }}" 
                               class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i> Voir
                            </a>
                            <a href="{{ route('employes.edit', $employe->id) }}" 
                               class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i> Éditer
                            </a>
                            <form action="{{ route('employes.destroy', $employe->id) }}" 
                                  method="POST" 
                                  style="display: inline;"
                                  onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet employé ?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Supprimer
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@else
    <div class="alert alert-info text-center">
        <h4>Aucun employé trouvé</h4>
        <p>Commencez par <a href="{{ route('employes.create') }}">ajouter un employé</a>.</p>
    </div>
@endif
@endsection
