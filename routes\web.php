<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\EmployeController;
use App\Http\Controllers\FormationController;
use App\Http\Controllers\ContactController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Routes protégées pour les employés (nécessite authentification)
    Route::resource('employes', EmployeController::class);

    // Routes protégées pour les formations (nécessite authentification)
    Route::resource('formations', FormationController::class);

    // Route spéciale pour voir les formations d'un employé
    Route::get('/employes/{employe_id}/formations', [FormationController::class, 'showFormationsByEmploye'])
        ->name('employes.formations');
});

// Routes publiques (accessibles sans authentification)
Route::get('/contact', [ContactController::class, 'show'])->name('contact.form');
Route::post('/contact', [ContactController::class, 'send'])->name('contact.send');

require __DIR__.'/auth.php';
